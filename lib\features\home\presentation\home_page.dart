import 'package:flutter/material.dart';
import '../../../shared/widgets/stats_card.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Dashboard')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>umn(
          children: const [
            StatsCard(title: 'Datasets', value: '12', icon: Icons.table_chart),
            Si<PERSON><PERSON><PERSON>(height: 16),
            StatsCard(title: 'Exports', value: '5', icon: Icons.download),
          ],
        ),
      ),
    );
  }
} 