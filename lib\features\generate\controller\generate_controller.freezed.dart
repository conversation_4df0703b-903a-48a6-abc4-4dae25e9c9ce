// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'generate_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$GenerateState {

 bool get isLoading; String get errorMessage; List<Map<String, dynamic>> get dataset;
/// Create a copy of GenerateState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GenerateStateCopyWith<GenerateState> get copyWith => _$GenerateStateCopyWithImpl<GenerateState>(this as GenerateState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GenerateState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&const DeepCollectionEquality().equals(other.dataset, dataset));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,errorMessage,const DeepCollectionEquality().hash(dataset));

@override
String toString() {
  return 'GenerateState(isLoading: $isLoading, errorMessage: $errorMessage, dataset: $dataset)';
}


}

/// @nodoc
abstract mixin class $GenerateStateCopyWith<$Res>  {
  factory $GenerateStateCopyWith(GenerateState value, $Res Function(GenerateState) _then) = _$GenerateStateCopyWithImpl;
@useResult
$Res call({
 bool isLoading, String errorMessage, List<Map<String, dynamic>> dataset
});




}
/// @nodoc
class _$GenerateStateCopyWithImpl<$Res>
    implements $GenerateStateCopyWith<$Res> {
  _$GenerateStateCopyWithImpl(this._self, this._then);

  final GenerateState _self;
  final $Res Function(GenerateState) _then;

/// Create a copy of GenerateState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isLoading = null,Object? errorMessage = null,Object? dataset = null,}) {
  return _then(_self.copyWith(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,dataset: null == dataset ? _self.dataset : dataset // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,
  ));
}

}


/// Adds pattern-matching-related methods to [GenerateState].
extension GenerateStatePatterns on GenerateState {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _GenerateState value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _GenerateState() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _GenerateState value)  $default,){
final _that = this;
switch (_that) {
case _GenerateState():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _GenerateState value)?  $default,){
final _that = this;
switch (_that) {
case _GenerateState() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( bool isLoading,  String errorMessage,  List<Map<String, dynamic>> dataset)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _GenerateState() when $default != null:
return $default(_that.isLoading,_that.errorMessage,_that.dataset);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( bool isLoading,  String errorMessage,  List<Map<String, dynamic>> dataset)  $default,) {final _that = this;
switch (_that) {
case _GenerateState():
return $default(_that.isLoading,_that.errorMessage,_that.dataset);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( bool isLoading,  String errorMessage,  List<Map<String, dynamic>> dataset)?  $default,) {final _that = this;
switch (_that) {
case _GenerateState() when $default != null:
return $default(_that.isLoading,_that.errorMessage,_that.dataset);case _:
  return null;

}
}

}

/// @nodoc


class _GenerateState implements GenerateState {
  const _GenerateState({this.isLoading = false, this.errorMessage = '', final  List<Map<String, dynamic>> dataset = const []}): _dataset = dataset;
  

@override@JsonKey() final  bool isLoading;
@override@JsonKey() final  String errorMessage;
 final  List<Map<String, dynamic>> _dataset;
@override@JsonKey() List<Map<String, dynamic>> get dataset {
  if (_dataset is EqualUnmodifiableListView) return _dataset;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_dataset);
}


/// Create a copy of GenerateState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GenerateStateCopyWith<_GenerateState> get copyWith => __$GenerateStateCopyWithImpl<_GenerateState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GenerateState&&(identical(other.isLoading, isLoading) || other.isLoading == isLoading)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&const DeepCollectionEquality().equals(other._dataset, _dataset));
}


@override
int get hashCode => Object.hash(runtimeType,isLoading,errorMessage,const DeepCollectionEquality().hash(_dataset));

@override
String toString() {
  return 'GenerateState(isLoading: $isLoading, errorMessage: $errorMessage, dataset: $dataset)';
}


}

/// @nodoc
abstract mixin class _$GenerateStateCopyWith<$Res> implements $GenerateStateCopyWith<$Res> {
  factory _$GenerateStateCopyWith(_GenerateState value, $Res Function(_GenerateState) _then) = __$GenerateStateCopyWithImpl;
@override @useResult
$Res call({
 bool isLoading, String errorMessage, List<Map<String, dynamic>> dataset
});




}
/// @nodoc
class __$GenerateStateCopyWithImpl<$Res>
    implements _$GenerateStateCopyWith<$Res> {
  __$GenerateStateCopyWithImpl(this._self, this._then);

  final _GenerateState _self;
  final $Res Function(_GenerateState) _then;

/// Create a copy of GenerateState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isLoading = null,Object? errorMessage = null,Object? dataset = null,}) {
  return _then(_GenerateState(
isLoading: null == isLoading ? _self.isLoading : isLoading // ignore: cast_nullable_to_non_nullable
as bool,errorMessage: null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,dataset: null == dataset ? _self._dataset : dataset // ignore: cast_nullable_to_non_nullable
as List<Map<String, dynamic>>,
  ));
}


}

// dart format on
