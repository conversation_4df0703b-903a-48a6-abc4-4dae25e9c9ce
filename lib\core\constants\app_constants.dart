import 'package:flutter_dotenv/flutter_dotenv.dart';

class AppConstants {
  // App Information
  static String get appName => dotenv.env['APP_NAME'] ?? 'Synthara Mobile';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
  static bool get debugMode => dotenv.env['DEBUG_MODE']?.toLowerCase() == 'true';

  // API Configuration
  static String get openRouterApiKey => dotenv.env['OPENROUTER_API_KEY'] ?? '';
  static String get openRouterBaseUrl => dotenv.env['OPENROUTER_BASE_URL'] ?? 'https://openrouter.ai/api/v1';
  static String get apiBaseUrl => dotenv.env['API_BASE_URL'] ?? 'https://api.synthara.app';

  // API Endpoints
  static String get webScrapingEndpoint => dotenv.env['WEB_SCRAPING_ENDPOINT'] ?? '/api/scrape';
  static String get datasetGenerationEndpoint => dotenv.env['DATASET_GENERATION_ENDPOINT'] ?? '/api/generate';

  // Firebase Configuration
  static String get firebaseProjectId => dotenv.env['FIREBASE_PROJECT_ID'] ?? '';
  static String get firebaseApiKey => dotenv.env['FIREBASE_API_KEY'] ?? '';

  // Feature Flags
  static bool get enableAnalytics => dotenv.env['ENABLE_ANALYTICS']?.toLowerCase() == 'true';
  static bool get enableCaching => dotenv.env['ENABLE_CACHING']?.toLowerCase() == 'true';
  static bool get enableOfflineMode => dotenv.env['ENABLE_OFFLINE_MODE']?.toLowerCase() == 'true';

  // Rate Limiting
  static int get maxConcurrentRequests => int.tryParse(dotenv.env['MAX_CONCURRENT_REQUESTS'] ?? '10') ?? 10;
  static int get requestTimeoutSeconds => int.tryParse(dotenv.env['REQUEST_TIMEOUT_SECONDS'] ?? '30') ?? 30;

  // Storage Configuration
  static int get cacheDurationHours => int.tryParse(dotenv.env['CACHE_DURATION_HOURS'] ?? '24') ?? 24;
  static int get maxCacheSizeMB => int.tryParse(dotenv.env['MAX_CACHE_SIZE_MB'] ?? '100') ?? 100;

  // Storage Keys
  static const String userSettingsKey = 'user_settings';
  static const String userProfileKey = 'user_profile';
  static const String cachedDatasetsKey = 'cached_datasets';
  static const String themePreferenceKey = 'theme_preference';

  // API Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'User-Agent': '$appName/$appVersion',
    'X-App-Version': appVersion,
  };

  static Map<String, String> get openRouterHeaders => {
    ...defaultHeaders,
    'Authorization': 'Bearer $openRouterApiKey',
    'HTTP-Referer': 'https://synthara.app',
    'X-Title': appName,
  };
}