import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../services/environment_service.dart';
import '../../features/home/<USER>/home_controller.dart';
import '../../features/generate/controller/generate_controller.dart';

// Core Service Providers
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageServiceImpl();
});

// Environment Provider
final environmentProvider = Provider<EnvironmentService>((ref) {
  return EnvironmentService();
});

// Theme Provider
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeState>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return ThemeNotifier(storage);
});

// App State Provider
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

// Connectivity Provider
final connectivityProvider = StreamProvider<bool>((ref) {
  // This would be implemented with connectivity_plus package
  // For now, return a stream that always emits true
  return Stream.value(true);
});

// Theme State Management
enum AppThemeMode { light, dark, system }

class ThemeState {
  final AppThemeMode themeMode;
  final bool isDarkMode;

  const ThemeState({
    required this.themeMode,
    required this.isDarkMode,
  });

  ThemeState copyWith({
    AppThemeMode? themeMode,
    bool? isDarkMode,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      isDarkMode: isDarkMode ?? this.isDarkMode,
    );
  }
}

class ThemeNotifier extends StateNotifier<ThemeState> {
  final StorageService _storage;
  static const String _themeKey = 'app_theme_mode';

  ThemeNotifier(this._storage) : super(const ThemeState(
    themeMode: AppThemeMode.system,
    isDarkMode: false,
  )) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final savedTheme = await _storage.getString(_themeKey);
    if (savedTheme != null) {
      final themeMode = AppThemeMode.values.firstWhere(
        (mode) => mode.toString() == savedTheme,
        orElse: () => AppThemeMode.system,
      );
      state = state.copyWith(themeMode: themeMode);
    }
  }

  Future<void> setThemeMode(AppThemeMode mode) async {
    await _storage.setString(_themeKey, mode.toString());
    state = state.copyWith(themeMode: mode);
  }

  void setDarkMode(bool isDark) {
    state = state.copyWith(isDarkMode: isDark);
  }
}

// App State Management
class AppState {
  final bool isInitialized;
  final bool isOnline;
  final String? errorMessage;

  const AppState({
    required this.isInitialized,
    required this.isOnline,
    this.errorMessage,
  });

  AppState copyWith({
    bool? isInitialized,
    bool? isOnline,
    String? errorMessage,
  }) {
    return AppState(
      isInitialized: isInitialized ?? this.isInitialized,
      isOnline: isOnline ?? this.isOnline,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(const AppState(
    isInitialized: false,
    isOnline: true,
  ));

  void setInitialized(bool initialized) {
    state = state.copyWith(isInitialized: initialized);
  }

  void setOnlineStatus(bool isOnline) {
    state = state.copyWith(isOnline: isOnline);
  }

  void setError(String? error) {
    state = state.copyWith(errorMessage: error);
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}
