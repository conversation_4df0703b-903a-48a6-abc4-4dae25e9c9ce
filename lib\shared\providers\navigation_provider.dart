import 'package:flutter_riverpod/flutter_riverpod.dart';

// Navigation State Provider
final navigationProvider = StateNotifierProvider<NavigationNotifier, NavigationState>((ref) {
  return NavigationNotifier();
});

class NavigationState {
  final int currentIndex;
  final String currentRoute;
  final Map<String, dynamic> routeArguments;

  const NavigationState({
    required this.currentIndex,
    required this.currentRoute,
    this.routeArguments = const {},
  });

  NavigationState copyWith({
    int? currentIndex,
    String? currentRoute,
    Map<String, dynamic>? routeArguments,
  }) {
    return NavigationState(
      currentIndex: currentIndex ?? this.currentIndex,
      currentRoute: currentRoute ?? this.currentRoute,
      routeArguments: routeArguments ?? this.routeArguments,
    );
  }
}

class NavigationNotifier extends StateNotifier<NavigationState> {
  NavigationNotifier() : super(const NavigationState(
    currentIndex: 0,
    currentRoute: '/home',
  ));

  void setCurrentIndex(int index) {
    final routes = ['/home', '/generate', '/preview', '/profile', '/settings'];
    if (index >= 0 && index < routes.length) {
      state = state.copyWith(
        currentIndex: index,
        currentRoute: routes[index],
      );
    }
  }

  void setCurrentRoute(String route, {Map<String, dynamic>? arguments}) {
    state = state.copyWith(
      currentRoute: route,
      routeArguments: arguments ?? {},
    );
  }

  void navigateToHome() => setCurrentIndex(0);
  void navigateToGenerate() => setCurrentIndex(1);
  void navigateToPreview() => setCurrentIndex(2);
  void navigateToProfile() => setCurrentIndex(3);
  void navigateToSettings() => setCurrentIndex(4);
}
