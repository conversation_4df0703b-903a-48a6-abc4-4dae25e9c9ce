# Synthara Mobile Environment Configuration Example
# Copy this file to .env and fill in your actual values

# OpenRouter API Configuration
# Get your API key from https://openrouter.ai/
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Firebase Configuration (optional)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-firebase-api-key

# App Configuration
APP_NAME=Synthara Mobile
APP_VERSION=1.0.0
DEBUG_MODE=true

# API Endpoints
API_BASE_URL=https://api.synthara.app
WEB_SCRAPING_ENDPOINT=/api/scrape
DATASET_GENERATION_ENDPOINT=/api/generate

# Feature Flags
ENABLE_ANALYTICS=true
ENABLE_CACHING=true
ENABLE_OFFLINE_MODE=false

# Rate Limiting
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=30

# Storage Configuration
CACHE_DURATION_HOURS=24
MAX_CACHE_SIZE_MB=100
