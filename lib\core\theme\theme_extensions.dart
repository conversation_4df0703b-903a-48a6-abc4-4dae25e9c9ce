import 'package:flutter/material.dart';
import 'app_colors.dart';

// Custom theme extension for additional colors and properties
@immutable
class CustomColors extends ThemeExtension<CustomColors> {
  final Color? success;
  final Color? warning;
  final Color? info;
  final Color? danger;
  final Color? cardBackground;
  final Color? shimmerBase;
  final Color? shimmerHighlight;
  final Gradient? primaryGradient;
  final Gradient? backgroundGradient;

  const CustomColors({
    this.success,
    this.warning,
    this.info,
    this.danger,
    this.cardBackground,
    this.shimmerBase,
    this.shimmerHighlight,
    this.primaryGradient,
    this.backgroundGradient,
  });

  @override
  CustomColors copyWith({
    Color? success,
    Color? warning,
    Color? info,
    Color? danger,
    Color? cardBackground,
    Color? shimmerBase,
    Color? shimmerHighlight,
    Gradient? primaryGradient,
    Gradient? backgroundGradient,
  }) {
    return CustomColors(
      success: success ?? this.success,
      warning: warning ?? this.warning,
      info: info ?? this.info,
      danger: danger ?? this.danger,
      cardBackground: cardBackground ?? this.cardBackground,
      shimmerBase: shimmerBase ?? this.shimmerBase,
      shimmerHighlight: shimmerHighlight ?? this.shimmerHighlight,
      primaryGradient: primaryGradient ?? this.primaryGradient,
      backgroundGradient: backgroundGradient ?? this.backgroundGradient,
    );
  }

  @override
  CustomColors lerp(ThemeExtension<CustomColors>? other, double t) {
    if (other is! CustomColors) {
      return this;
    }
    return CustomColors(
      success: Color.lerp(success, other.success, t),
      warning: Color.lerp(warning, other.warning, t),
      info: Color.lerp(info, other.info, t),
      danger: Color.lerp(danger, other.danger, t),
      cardBackground: Color.lerp(cardBackground, other.cardBackground, t),
      shimmerBase: Color.lerp(shimmerBase, other.shimmerBase, t),
      shimmerHighlight: Color.lerp(shimmerHighlight, other.shimmerHighlight, t),
      primaryGradient: Gradient.lerp(primaryGradient, other.primaryGradient, t),
      backgroundGradient: Gradient.lerp(backgroundGradient, other.backgroundGradient, t),
    );
  }

  // Light theme custom colors
  static const CustomColors light = CustomColors(
    success: AppColors.success,
    warning: AppColors.warning,
    info: AppColors.info,
    danger: AppColors.danger,
    cardBackground: AppColors.lightSurface,
    shimmerBase: AppColors.grey200,
    shimmerHighlight: AppColors.grey100,
    primaryGradient: AppColors.primaryGradient,
    backgroundGradient: LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [AppColors.lightBackground, AppColors.lightSurface],
    ),
  );

  // Dark theme custom colors
  static const CustomColors dark = CustomColors(
    success: AppColors.success,
    warning: AppColors.warning,
    info: AppColors.info,
    danger: AppColors.danger,
    cardBackground: AppColors.darkSurface,
    shimmerBase: AppColors.grey800,
    shimmerHighlight: AppColors.grey700,
    primaryGradient: AppColors.primaryGradient,
    backgroundGradient: AppColors.darkGradient,
  );
}

// Extension to easily access custom colors from BuildContext
extension CustomColorsExtension on BuildContext {
  CustomColors get customColors {
    return Theme.of(this).extension<CustomColors>() ?? CustomColors.light;
  }
}

// Custom spacing theme extension
@immutable
class CustomSpacing extends ThemeExtension<CustomSpacing> {
  final double xs;
  final double sm;
  final double md;
  final double lg;
  final double xl;
  final double xxl;

  const CustomSpacing({
    required this.xs,
    required this.sm,
    required this.md,
    required this.lg,
    required this.xl,
    required this.xxl,
  });

  @override
  CustomSpacing copyWith({
    double? xs,
    double? sm,
    double? md,
    double? lg,
    double? xl,
    double? xxl,
  }) {
    return CustomSpacing(
      xs: xs ?? this.xs,
      sm: sm ?? this.sm,
      md: md ?? this.md,
      lg: lg ?? this.lg,
      xl: xl ?? this.xl,
      xxl: xxl ?? this.xxl,
    );
  }

  @override
  CustomSpacing lerp(ThemeExtension<CustomSpacing>? other, double t) {
    if (other is! CustomSpacing) {
      return this;
    }
    return CustomSpacing(
      xs: lerpDouble(xs, other.xs, t) ?? xs,
      sm: lerpDouble(sm, other.sm, t) ?? sm,
      md: lerpDouble(md, other.md, t) ?? md,
      lg: lerpDouble(lg, other.lg, t) ?? lg,
      xl: lerpDouble(xl, other.xl, t) ?? xl,
      xxl: lerpDouble(xxl, other.xxl, t) ?? xxl,
    );
  }

  static const CustomSpacing standard = CustomSpacing(
    xs: 4.0,
    sm: 8.0,
    md: 16.0,
    lg: 24.0,
    xl: 32.0,
    xxl: 48.0,
  );
}

// Extension to easily access custom spacing from BuildContext
extension CustomSpacingExtension on BuildContext {
  CustomSpacing get spacing {
    return Theme.of(this).extension<CustomSpacing>() ?? CustomSpacing.standard;
  }
}

// Helper function to get double lerp
double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}
