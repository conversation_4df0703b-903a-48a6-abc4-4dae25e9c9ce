import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_settings.freezed.dart';
part 'app_settings.g.dart';

@freezed
abstract class AppSettings with _$AppSettings {
  const factory AppSettings({
    @Default(ThemeMode.system) ThemeMode themeMode,
    @Default('en') String language,
    @Default(true) bool notificationsEnabled,
    @Default(true) bool analyticsEnabled,
    @Default(DataExportFormat.csv) DataExportFormat defaultExportFormat,
    @Default(10) int maxConcurrentRequests,
    @Default(true) bool cacheEnabled,
    String? openRouterApiKey,
  }) = _AppSettings;

  factory AppSettings.fromJson(Map<String, dynamic> json) => _$AppSettingsFromJson(json);
}

enum ThemeMode {
  light,
  dark,
  system,
}

enum DataExportFormat {
  csv,
  json,
  xlsx,
}
