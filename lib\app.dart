import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'shared/widgets/bottom_nav_bar.dart';
import 'features/generate/presentation/generate_page.dart';
import 'features/generate/presentation/preview_page.dart';
import 'features/home/<USER>/home_page.dart';
import 'features/profile/presentation/profile_page.dart';
import 'features/settings/presentation/settings_page.dart';
import 'core/theme/app_theme.dart';
import 'shared/providers/providers.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class App extends ConsumerWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final navigationState = ref.watch(navigationProvider);
    final analytics = FirebaseAnalytics.instance;

    final pages = const [
      HomePage(), // Home
      GeneratePage(), // Generate
      PreviewPage(), // Preview
      ProfilePage(), // Profile
      SettingsPage(), // Settings
    ];

    return MaterialApp(
      title: 'Synthara Mobile',
      theme: AppTheme.light,
      darkTheme: AppTheme.dark,
      themeMode: ThemeMode.system,
      navigatorObservers: [FirebaseAnalyticsObserver(analytics: analytics)],
      home: Scaffold(
        body: pages[navigationState.currentIndex],
        bottomNavigationBar: BottomNavBar(
          currentIndex: navigationState.currentIndex,
          onTap: (index) => ref.read(navigationProvider.notifier).setCurrentIndex(index),
        ),
      ),
    );
  }
}
