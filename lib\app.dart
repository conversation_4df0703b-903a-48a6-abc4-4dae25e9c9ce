import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'shared/widgets/bottom_nav_bar.dart';
import 'features/generate/presentation/generate_page.dart';
import 'features/generate/presentation/preview_page.dart';
import 'features/home/<USER>/home_page.dart';
import 'features/profile/presentation/profile_page.dart';
import 'features/settings/presentation/settings_page.dart';
import 'core/theme/app_theme.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class App extends StatefulWidget {
  const App({super.key});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  int _currentIndex = 0;

  final List<Widget> _pages = const [
    HomePage(), // Home
    GeneratePage(), // Generate
    PreviewPage(), // Preview
    ProfilePage(), // Profile
    SettingsPage(), // Settings
  ];

  @override
  Widget build(BuildContext context) {
    final analytics = FirebaseAnalytics.instance;
    return ProviderScope(
      child: MaterialApp(
        title: 'Synthara Mobile',
        theme: AppTheme.light,
        darkTheme: AppTheme.dark,
        themeMode: ThemeMode.system,
        navigatorObservers: [FirebaseAnalyticsObserver(analytics: analytics)],
        home: Scaffold(
          body: _pages[_currentIndex],
          bottomNavigationBar: BottomNavBar(
            currentIndex: _currentIndex,
            onTap: (index) => setState(() => _currentIndex = index),
          ),
        ),
      ),
    );
  }
}
