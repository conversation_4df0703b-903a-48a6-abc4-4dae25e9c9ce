// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) => _AppSettings(
  themeMode:
      $enumDecodeNullable(_$ThemeModeEnumMap, json['themeMode']) ??
      ThemeMode.system,
  language: json['language'] as String? ?? 'en',
  notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
  analyticsEnabled: json['analyticsEnabled'] as bool? ?? true,
  defaultExportFormat:
      $enumDecodeNullable(
        _$DataExportFormatEnumMap,
        json['defaultExportFormat'],
      ) ??
      DataExportFormat.csv,
  maxConcurrentRequests: (json['maxConcurrentRequests'] as num?)?.toInt() ?? 10,
  cacheEnabled: json['cacheEnabled'] as bool? ?? true,
  openRouterApiKey: json['openRouterApiKey'] as String?,
);

Map<String, dynamic> _$AppSettingsToJson(_AppSettings instance) =>
    <String, dynamic>{
      'themeMode': _$ThemeModeEnumMap[instance.themeMode]!,
      'language': instance.language,
      'notificationsEnabled': instance.notificationsEnabled,
      'analyticsEnabled': instance.analyticsEnabled,
      'defaultExportFormat':
          _$DataExportFormatEnumMap[instance.defaultExportFormat]!,
      'maxConcurrentRequests': instance.maxConcurrentRequests,
      'cacheEnabled': instance.cacheEnabled,
      'openRouterApiKey': instance.openRouterApiKey,
    };

const _$ThemeModeEnumMap = {
  ThemeMode.light: 'light',
  ThemeMode.dark: 'dark',
  ThemeMode.system: 'system',
};

const _$DataExportFormatEnumMap = {
  DataExportFormat.csv: 'csv',
  DataExportFormat.json: 'json',
  DataExportFormat.xlsx: 'xlsx',
};
