import 'dart:convert';
import 'package:flutter/foundation.dart';

abstract class StorageService {
  Future<void> setString(String key, String value);
  Future<String?> getString(String key);
  Future<void> setBool(String key, bool value);
  Future<bool?> getBool(String key);
  Future<void> setInt(String key, int value);
  Future<int?> getInt(String key);
  Future<void> setDouble(String key, double value);
  Future<double?> getDouble(String key);
  Future<void> setObject<T>(String key, T object);
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson);
  Future<void> remove(String key);
  Future<void> clear();
}

class StorageServiceImpl implements StorageService {
  // For now, using in-memory storage
  // In production, this would use SharedPreferences or secure storage
  final Map<String, dynamic> _storage = {};

  @override
  Future<void> setString(String key, String value) async {
    _storage[key] = value;
    if (kDebugMode) {
      print('Storage: Set string $key = $value');
    }
  }

  @override
  Future<String?> getString(String key) async {
    final value = _storage[key] as String?;
    if (kDebugMode) {
      print('Storage: Get string $key = $value');
    }
    return value;
  }

  @override
  Future<void> setBool(String key, bool value) async {
    _storage[key] = value;
    if (kDebugMode) {
      print('Storage: Set bool $key = $value');
    }
  }

  @override
  Future<bool?> getBool(String key) async {
    final value = _storage[key] as bool?;
    if (kDebugMode) {
      print('Storage: Get bool $key = $value');
    }
    return value;
  }

  @override
  Future<void> setInt(String key, int value) async {
    _storage[key] = value;
    if (kDebugMode) {
      print('Storage: Set int $key = $value');
    }
  }

  @override
  Future<int?> getInt(String key) async {
    final value = _storage[key] as int?;
    if (kDebugMode) {
      print('Storage: Get int $key = $value');
    }
    return value;
  }

  @override
  Future<void> setDouble(String key, double value) async {
    _storage[key] = value;
    if (kDebugMode) {
      print('Storage: Set double $key = $value');
    }
  }

  @override
  Future<double?> getDouble(String key) async {
    final value = _storage[key] as double?;
    if (kDebugMode) {
      print('Storage: Get double $key = $value');
    }
    return value;
  }

  @override
  Future<void> setObject<T>(String key, T object) async {
    final jsonString = jsonEncode(object);
    await setString(key, jsonString);
  }

  @override
  Future<T?> getObject<T>(String key, T Function(Map<String, dynamic>) fromJson) async {
    final jsonString = await getString(key);
    if (jsonString == null) return null;
    
    try {
      final jsonMap = jsonDecode(jsonString) as Map<String, dynamic>;
      return fromJson(jsonMap);
    } catch (e) {
      if (kDebugMode) {
        print('Storage: Error parsing object for key $key: $e');
      }
      return null;
    }
  }

  @override
  Future<void> remove(String key) async {
    _storage.remove(key);
    if (kDebugMode) {
      print('Storage: Removed $key');
    }
  }

  @override
  Future<void> clear() async {
    _storage.clear();
    if (kDebugMode) {
      print('Storage: Cleared all data');
    }
  }
}
