import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class EnvironmentService {
  static const _secureStorage = FlutterSecureStorage();
  
  // Secure storage keys
  static const String _openRouterApiKeySecure = 'openrouter_api_key_secure';
  static const String _firebaseApiKeySecure = 'firebase_api_key_secure';

  /// Initialize environment service
  static Future<void> initialize() async {
    try {
      await dotenv.load(fileName: ".env");
      if (kDebugMode) {
        print('Environment variables loaded successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Warning: .env file not found. Using default configuration.');
      }
    }
  }

  /// Get environment variable with fallback
  static String getEnv(String key, {String fallback = ''}) {
    return dotenv.env[key] ?? fallback;
  }

  /// Get boolean environment variable
  static bool getBoolEnv(String key, {bool fallback = false}) {
    final value = dotenv.env[key]?.toLowerCase();
    if (value == null) return fallback;
    return value == 'true' || value == '1' || value == 'yes';
  }

  /// Get integer environment variable
  static int getIntEnv(String key, {int fallback = 0}) {
    final value = dotenv.env[key];
    if (value == null) return fallback;
    return int.tryParse(value) ?? fallback;
  }

  /// Get double environment variable
  static double getDoubleEnv(String key, {double fallback = 0.0}) {
    final value = dotenv.env[key];
    if (value == null) return fallback;
    return double.tryParse(value) ?? fallback;
  }

  /// Store sensitive API key securely
  static Future<void> storeApiKeySecurely(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      if (kDebugMode) {
        print('API key stored securely for: $key');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error storing API key securely: $e');
      }
    }
  }

  /// Retrieve sensitive API key securely
  static Future<String?> getApiKeySecurely(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      if (kDebugMode) {
        print('Error retrieving API key securely: $e');
      }
      return null;
    }
  }

  /// Get OpenRouter API key (from secure storage first, then env)
  static Future<String> getOpenRouterApiKey() async {
    // Try secure storage first
    final secureKey = await getApiKeySecurely(_openRouterApiKeySecure);
    if (secureKey != null && secureKey.isNotEmpty) {
      return secureKey;
    }
    
    // Fallback to environment variable
    return getEnv('OPENROUTER_API_KEY');
  }

  /// Store OpenRouter API key securely
  static Future<void> setOpenRouterApiKey(String apiKey) async {
    await storeApiKeySecurely(_openRouterApiKeySecure, apiKey);
  }

  /// Get Firebase API key (from secure storage first, then env)
  static Future<String> getFirebaseApiKey() async {
    final secureKey = await getApiKeySecurely(_firebaseApiKeySecure);
    if (secureKey != null && secureKey.isNotEmpty) {
      return secureKey;
    }
    
    return getEnv('FIREBASE_API_KEY');
  }

  /// Store Firebase API key securely
  static Future<void> setFirebaseApiKey(String apiKey) async {
    await storeApiKeySecurely(_firebaseApiKeySecure, apiKey);
  }

  /// Check if running in debug mode
  static bool get isDebugMode {
    return kDebugMode || getBoolEnv('DEBUG_MODE');
  }

  /// Check if analytics is enabled
  static bool get isAnalyticsEnabled {
    return getBoolEnv('ENABLE_ANALYTICS', fallback: true);
  }

  /// Check if caching is enabled
  static bool get isCachingEnabled {
    return getBoolEnv('ENABLE_CACHING', fallback: true);
  }

  /// Check if offline mode is enabled
  static bool get isOfflineModeEnabled {
    return getBoolEnv('ENABLE_OFFLINE_MODE', fallback: false);
  }

  /// Get maximum concurrent requests
  static int get maxConcurrentRequests {
    return getIntEnv('MAX_CONCURRENT_REQUESTS', fallback: 10);
  }

  /// Get request timeout in seconds
  static int get requestTimeoutSeconds {
    return getIntEnv('REQUEST_TIMEOUT_SECONDS', fallback: 30);
  }

  /// Clear all securely stored data
  static Future<void> clearSecureStorage() async {
    try {
      await _secureStorage.deleteAll();
      if (kDebugMode) {
        print('Secure storage cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing secure storage: $e');
      }
    }
  }

  /// Validate required environment variables
  static Future<List<String>> validateEnvironment() async {
    final missingKeys = <String>[];
    
    final openRouterKey = await getOpenRouterApiKey();
    if (openRouterKey.isEmpty) {
      missingKeys.add('OPENROUTER_API_KEY');
    }
    
    return missingKeys;
  }
}
