class AppDimensions {
  // Private constructor to prevent instantiation
  AppDimensions._();

  // Spacing
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing56 = 56.0;
  static const double spacing64 = 64.0;

  // Padding
  static const double paddingXS = spacing4;
  static const double paddingS = spacing8;
  static const double paddingM = spacing16;
  static const double paddingL = spacing24;
  static const double paddingXL = spacing32;
  static const double paddingXXL = spacing48;

  // Margins
  static const double marginXS = spacing4;
  static const double marginS = spacing8;
  static const double marginM = spacing16;
  static const double marginL = spacing24;
  static const double marginXL = spacing32;
  static const double marginXXL = spacing48;

  // Border Radius
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusCircular = 50.0;

  // Icon Sizes
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 40.0;
  static const double iconXXL = 48.0;

  // Button Heights
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  // Input Field Heights
  static const double inputHeightS = 40.0;
  static const double inputHeightM = 48.0;
  static const double inputHeightL = 56.0;

  // Card Dimensions
  static const double cardElevation = 2.0;
  static const double cardRadius = radiusL;
  static const double cardPadding = paddingM;

  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;

  // Bottom Navigation
  static const double bottomNavHeight = 80.0;
  static const double bottomNavElevation = 8.0;

  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 0.0;

  // Loading Indicator
  static const double loadingIndicatorSize = 24.0;
  static const double loadingIndicatorStrokeWidth = 3.0;

  // Avatar Sizes
  static const double avatarXS = 24.0;
  static const double avatarS = 32.0;
  static const double avatarM = 40.0;
  static const double avatarL = 56.0;
  static const double avatarXL = 72.0;
  static const double avatarXXL = 96.0;

  // Breakpoints for responsive design
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // Maximum content width
  static const double maxContentWidth = 1200.0;

  // Minimum touch target size (accessibility)
  static const double minTouchTarget = 44.0;

  // Animation Durations (in milliseconds)
  static const int animationDurationFast = 150;
  static const int animationDurationNormal = 300;
  static const int animationDurationSlow = 500;
  static const int animationDurationVerySlow = 1000;

  // Z-Index / Elevation levels
  static const double elevationNone = 0.0;
  static const double elevationLow = 1.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;
  static const double elevationMax = 24.0;
}
