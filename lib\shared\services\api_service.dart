import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../core/constants/app_constants.dart';

class ApiService {
  static const String _baseUrl = 'https://openrouter.ai/api/v1';
  final http.Client _client;
  
  ApiService({http.Client? client}) : _client = client ?? http.Client();

  Future<Map<String, dynamic>> post({
    required String endpoint,
    required Map<String, dynamic> data,
    Map<String, String>? headers,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl$endpoint');
      final defaultHeaders = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${AppConstants.openRouterApiKey}',
        'HTTP-Referer': 'https://synthara.app',
        'X-Title': 'Synthara Mobile',
      };

      final response = await _client.post(
        url,
        headers: {...defaultHeaders, ...?headers},
        body: jsonEncode(data),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw ApiException(
          statusCode: response.statusCode,
          message: 'API request failed: ${response.body}',
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(
        statusCode: 0,
        message: 'Network error: ${e.toString()}',
      );
    }
  }

  Future<Map<String, dynamic>> get({
    required String endpoint,
    Map<String, String>? headers,
    Map<String, String>? queryParams,
  }) async {
    try {
      var url = Uri.parse('$_baseUrl$endpoint');
      if (queryParams != null) {
        url = url.replace(queryParameters: queryParams);
      }

      final defaultHeaders = {
        'Authorization': 'Bearer ${AppConstants.openRouterApiKey}',
        'HTTP-Referer': 'https://synthara.app',
        'X-Title': 'Synthara Mobile',
      };

      final response = await _client.get(
        url,
        headers: {...defaultHeaders, ...?headers},
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw ApiException(
          statusCode: response.statusCode,
          message: 'API request failed: ${response.body}',
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException(
        statusCode: 0,
        message: 'Network error: ${e.toString()}',
      );
    }
  }

  void dispose() {
    _client.close();
  }
}

class ApiException implements Exception {
  final int statusCode;
  final String message;

  ApiException({
    required this.statusCode,
    required this.message,
  });

  @override
  String toString() => 'ApiException($statusCode): $message';
}
