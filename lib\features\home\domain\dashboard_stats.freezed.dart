// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_stats.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DashboardStats {

 int get totalDatasets; int get datasetsThisMonth; int get totalRows; double get averageAccuracy; List<RecentDataset> get recentDatasets;
/// Create a copy of DashboardStats
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DashboardStatsCopyWith<DashboardStats> get copyWith => _$DashboardStatsCopyWithImpl<DashboardStats>(this as DashboardStats, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DashboardStats&&(identical(other.totalDatasets, totalDatasets) || other.totalDatasets == totalDatasets)&&(identical(other.datasetsThisMonth, datasetsThisMonth) || other.datasetsThisMonth == datasetsThisMonth)&&(identical(other.totalRows, totalRows) || other.totalRows == totalRows)&&(identical(other.averageAccuracy, averageAccuracy) || other.averageAccuracy == averageAccuracy)&&const DeepCollectionEquality().equals(other.recentDatasets, recentDatasets));
}


@override
int get hashCode => Object.hash(runtimeType,totalDatasets,datasetsThisMonth,totalRows,averageAccuracy,const DeepCollectionEquality().hash(recentDatasets));

@override
String toString() {
  return 'DashboardStats(totalDatasets: $totalDatasets, datasetsThisMonth: $datasetsThisMonth, totalRows: $totalRows, averageAccuracy: $averageAccuracy, recentDatasets: $recentDatasets)';
}


}

/// @nodoc
abstract mixin class $DashboardStatsCopyWith<$Res>  {
  factory $DashboardStatsCopyWith(DashboardStats value, $Res Function(DashboardStats) _then) = _$DashboardStatsCopyWithImpl;
@useResult
$Res call({
 int totalDatasets, int datasetsThisMonth, int totalRows, double averageAccuracy, List<RecentDataset> recentDatasets
});




}
/// @nodoc
class _$DashboardStatsCopyWithImpl<$Res>
    implements $DashboardStatsCopyWith<$Res> {
  _$DashboardStatsCopyWithImpl(this._self, this._then);

  final DashboardStats _self;
  final $Res Function(DashboardStats) _then;

/// Create a copy of DashboardStats
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? totalDatasets = null,Object? datasetsThisMonth = null,Object? totalRows = null,Object? averageAccuracy = null,Object? recentDatasets = null,}) {
  return _then(_self.copyWith(
totalDatasets: null == totalDatasets ? _self.totalDatasets : totalDatasets // ignore: cast_nullable_to_non_nullable
as int,datasetsThisMonth: null == datasetsThisMonth ? _self.datasetsThisMonth : datasetsThisMonth // ignore: cast_nullable_to_non_nullable
as int,totalRows: null == totalRows ? _self.totalRows : totalRows // ignore: cast_nullable_to_non_nullable
as int,averageAccuracy: null == averageAccuracy ? _self.averageAccuracy : averageAccuracy // ignore: cast_nullable_to_non_nullable
as double,recentDatasets: null == recentDatasets ? _self.recentDatasets : recentDatasets // ignore: cast_nullable_to_non_nullable
as List<RecentDataset>,
  ));
}

}


/// Adds pattern-matching-related methods to [DashboardStats].
extension DashboardStatsPatterns on DashboardStats {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _DashboardStats value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _DashboardStats() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _DashboardStats value)  $default,){
final _that = this;
switch (_that) {
case _DashboardStats():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _DashboardStats value)?  $default,){
final _that = this;
switch (_that) {
case _DashboardStats() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( int totalDatasets,  int datasetsThisMonth,  int totalRows,  double averageAccuracy,  List<RecentDataset> recentDatasets)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _DashboardStats() when $default != null:
return $default(_that.totalDatasets,_that.datasetsThisMonth,_that.totalRows,_that.averageAccuracy,_that.recentDatasets);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( int totalDatasets,  int datasetsThisMonth,  int totalRows,  double averageAccuracy,  List<RecentDataset> recentDatasets)  $default,) {final _that = this;
switch (_that) {
case _DashboardStats():
return $default(_that.totalDatasets,_that.datasetsThisMonth,_that.totalRows,_that.averageAccuracy,_that.recentDatasets);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( int totalDatasets,  int datasetsThisMonth,  int totalRows,  double averageAccuracy,  List<RecentDataset> recentDatasets)?  $default,) {final _that = this;
switch (_that) {
case _DashboardStats() when $default != null:
return $default(_that.totalDatasets,_that.datasetsThisMonth,_that.totalRows,_that.averageAccuracy,_that.recentDatasets);case _:
  return null;

}
}

}

/// @nodoc


class _DashboardStats implements DashboardStats {
  const _DashboardStats({this.totalDatasets = 0, this.datasetsThisMonth = 0, this.totalRows = 0, this.averageAccuracy = 0.0, final  List<RecentDataset> recentDatasets = const []}): _recentDatasets = recentDatasets;
  

@override@JsonKey() final  int totalDatasets;
@override@JsonKey() final  int datasetsThisMonth;
@override@JsonKey() final  int totalRows;
@override@JsonKey() final  double averageAccuracy;
 final  List<RecentDataset> _recentDatasets;
@override@JsonKey() List<RecentDataset> get recentDatasets {
  if (_recentDatasets is EqualUnmodifiableListView) return _recentDatasets;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_recentDatasets);
}


/// Create a copy of DashboardStats
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DashboardStatsCopyWith<_DashboardStats> get copyWith => __$DashboardStatsCopyWithImpl<_DashboardStats>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DashboardStats&&(identical(other.totalDatasets, totalDatasets) || other.totalDatasets == totalDatasets)&&(identical(other.datasetsThisMonth, datasetsThisMonth) || other.datasetsThisMonth == datasetsThisMonth)&&(identical(other.totalRows, totalRows) || other.totalRows == totalRows)&&(identical(other.averageAccuracy, averageAccuracy) || other.averageAccuracy == averageAccuracy)&&const DeepCollectionEquality().equals(other._recentDatasets, _recentDatasets));
}


@override
int get hashCode => Object.hash(runtimeType,totalDatasets,datasetsThisMonth,totalRows,averageAccuracy,const DeepCollectionEquality().hash(_recentDatasets));

@override
String toString() {
  return 'DashboardStats(totalDatasets: $totalDatasets, datasetsThisMonth: $datasetsThisMonth, totalRows: $totalRows, averageAccuracy: $averageAccuracy, recentDatasets: $recentDatasets)';
}


}

/// @nodoc
abstract mixin class _$DashboardStatsCopyWith<$Res> implements $DashboardStatsCopyWith<$Res> {
  factory _$DashboardStatsCopyWith(_DashboardStats value, $Res Function(_DashboardStats) _then) = __$DashboardStatsCopyWithImpl;
@override @useResult
$Res call({
 int totalDatasets, int datasetsThisMonth, int totalRows, double averageAccuracy, List<RecentDataset> recentDatasets
});




}
/// @nodoc
class __$DashboardStatsCopyWithImpl<$Res>
    implements _$DashboardStatsCopyWith<$Res> {
  __$DashboardStatsCopyWithImpl(this._self, this._then);

  final _DashboardStats _self;
  final $Res Function(_DashboardStats) _then;

/// Create a copy of DashboardStats
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? totalDatasets = null,Object? datasetsThisMonth = null,Object? totalRows = null,Object? averageAccuracy = null,Object? recentDatasets = null,}) {
  return _then(_DashboardStats(
totalDatasets: null == totalDatasets ? _self.totalDatasets : totalDatasets // ignore: cast_nullable_to_non_nullable
as int,datasetsThisMonth: null == datasetsThisMonth ? _self.datasetsThisMonth : datasetsThisMonth // ignore: cast_nullable_to_non_nullable
as int,totalRows: null == totalRows ? _self.totalRows : totalRows // ignore: cast_nullable_to_non_nullable
as int,averageAccuracy: null == averageAccuracy ? _self.averageAccuracy : averageAccuracy // ignore: cast_nullable_to_non_nullable
as double,recentDatasets: null == recentDatasets ? _self._recentDatasets : recentDatasets // ignore: cast_nullable_to_non_nullable
as List<RecentDataset>,
  ));
}


}

/// @nodoc
mixin _$RecentDataset {

 String get id; String get name; String get prompt; DateTime get createdAt; int get rowCount; DatasetStatus get status;
/// Create a copy of RecentDataset
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RecentDatasetCopyWith<RecentDataset> get copyWith => _$RecentDatasetCopyWithImpl<RecentDataset>(this as RecentDataset, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RecentDataset&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.prompt, prompt) || other.prompt == prompt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.rowCount, rowCount) || other.rowCount == rowCount)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,id,name,prompt,createdAt,rowCount,status);

@override
String toString() {
  return 'RecentDataset(id: $id, name: $name, prompt: $prompt, createdAt: $createdAt, rowCount: $rowCount, status: $status)';
}


}

/// @nodoc
abstract mixin class $RecentDatasetCopyWith<$Res>  {
  factory $RecentDatasetCopyWith(RecentDataset value, $Res Function(RecentDataset) _then) = _$RecentDatasetCopyWithImpl;
@useResult
$Res call({
 String id, String name, String prompt, DateTime createdAt, int rowCount, DatasetStatus status
});




}
/// @nodoc
class _$RecentDatasetCopyWithImpl<$Res>
    implements $RecentDatasetCopyWith<$Res> {
  _$RecentDatasetCopyWithImpl(this._self, this._then);

  final RecentDataset _self;
  final $Res Function(RecentDataset) _then;

/// Create a copy of RecentDataset
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? prompt = null,Object? createdAt = null,Object? rowCount = null,Object? status = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,prompt: null == prompt ? _self.prompt : prompt // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,rowCount: null == rowCount ? _self.rowCount : rowCount // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as DatasetStatus,
  ));
}

}


/// Adds pattern-matching-related methods to [RecentDataset].
extension RecentDatasetPatterns on RecentDataset {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _RecentDataset value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _RecentDataset() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _RecentDataset value)  $default,){
final _that = this;
switch (_that) {
case _RecentDataset():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _RecentDataset value)?  $default,){
final _that = this;
switch (_that) {
case _RecentDataset() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( String id,  String name,  String prompt,  DateTime createdAt,  int rowCount,  DatasetStatus status)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _RecentDataset() when $default != null:
return $default(_that.id,_that.name,_that.prompt,_that.createdAt,_that.rowCount,_that.status);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( String id,  String name,  String prompt,  DateTime createdAt,  int rowCount,  DatasetStatus status)  $default,) {final _that = this;
switch (_that) {
case _RecentDataset():
return $default(_that.id,_that.name,_that.prompt,_that.createdAt,_that.rowCount,_that.status);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( String id,  String name,  String prompt,  DateTime createdAt,  int rowCount,  DatasetStatus status)?  $default,) {final _that = this;
switch (_that) {
case _RecentDataset() when $default != null:
return $default(_that.id,_that.name,_that.prompt,_that.createdAt,_that.rowCount,_that.status);case _:
  return null;

}
}

}

/// @nodoc


class _RecentDataset implements RecentDataset {
  const _RecentDataset({required this.id, required this.name, required this.prompt, required this.createdAt, required this.rowCount, required this.status});
  

@override final  String id;
@override final  String name;
@override final  String prompt;
@override final  DateTime createdAt;
@override final  int rowCount;
@override final  DatasetStatus status;

/// Create a copy of RecentDataset
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RecentDatasetCopyWith<_RecentDataset> get copyWith => __$RecentDatasetCopyWithImpl<_RecentDataset>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RecentDataset&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.prompt, prompt) || other.prompt == prompt)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.rowCount, rowCount) || other.rowCount == rowCount)&&(identical(other.status, status) || other.status == status));
}


@override
int get hashCode => Object.hash(runtimeType,id,name,prompt,createdAt,rowCount,status);

@override
String toString() {
  return 'RecentDataset(id: $id, name: $name, prompt: $prompt, createdAt: $createdAt, rowCount: $rowCount, status: $status)';
}


}

/// @nodoc
abstract mixin class _$RecentDatasetCopyWith<$Res> implements $RecentDatasetCopyWith<$Res> {
  factory _$RecentDatasetCopyWith(_RecentDataset value, $Res Function(_RecentDataset) _then) = __$RecentDatasetCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String prompt, DateTime createdAt, int rowCount, DatasetStatus status
});




}
/// @nodoc
class __$RecentDatasetCopyWithImpl<$Res>
    implements _$RecentDatasetCopyWith<$Res> {
  __$RecentDatasetCopyWithImpl(this._self, this._then);

  final _RecentDataset _self;
  final $Res Function(_RecentDataset) _then;

/// Create a copy of RecentDataset
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? prompt = null,Object? createdAt = null,Object? rowCount = null,Object? status = null,}) {
  return _then(_RecentDataset(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,prompt: null == prompt ? _self.prompt : prompt // ignore: cast_nullable_to_non_nullable
as String,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,rowCount: null == rowCount ? _self.rowCount : rowCount // ignore: cast_nullable_to_non_nullable
as int,status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as DatasetStatus,
  ));
}


}

// dart format on
