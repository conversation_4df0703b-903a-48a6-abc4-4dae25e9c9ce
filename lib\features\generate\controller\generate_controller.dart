import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../data/dataset_repository.dart';
import '../data/dataset_repository_impl.dart';

part 'generate_controller.freezed.dart';

@freezed
class GenerateState with _$GenerateState {
  const factory GenerateState({
    @Default(false) bool isLoading,
    @Default('') String errorMessage,
    @Default([]) List<Map<String, dynamic>> dataset,
  }) = _GenerateState;
}

final generateControllerProvider = StateNotifierProvider<GenerateController, GenerateState>((ref) {
  return GenerateController(DatasetRepositoryImpl());
});

class GenerateController extends StateNotifier<GenerateState> {
  final DatasetRepository repository;
  GenerateController(this.repository) : super(const GenerateState());

  Future<void> executeGenerateDataset(String prompt) async {
    state = state.copyWith(isLoading: true, errorMessage: '');
    try {
      final data = await repository.fetchDataset(prompt: prompt);
      state = state.copyWith(dataset: data, isLoading: false);
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString(), isLoading: false);
    }
  }
} 