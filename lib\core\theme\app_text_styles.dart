import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();

  // Font Family
  static const String primaryFontFamily = 'Inter';
  static const String headingFontFamily = 'Space Grotesk';

  // Base Text Styles
  static const TextStyle _baseTextStyle = TextStyle(
    fontFamily: primaryFontFamily,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.0,
  );

  static const TextStyle _baseHeadingStyle = TextStyle(
    fontFamily: headingFontFamily,
    fontWeight: FontWeight.w600,
    letterSpacing: -0.5,
  );

  // Light Theme Text Styles
  static final TextTheme lightTextTheme = TextTheme(
    // Display Styles
    displayLarge: _baseHeadingStyle.copyWith(
      fontSize: 57,
      fontWeight: FontWeight.w700,
      color: AppColors.lightOnBackground,
      height: 1.12,
    ),
    displayMedium: _baseHeadingStyle.copyWith(
      fontSize: 45,
      fontWeight: FontWeight.w700,
      color: AppColors.lightOnBackground,
      height: 1.16,
    ),
    displaySmall: _baseHeadingStyle.copyWith(
      fontSize: 36,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnBackground,
      height: 1.22,
    ),

    // Headline Styles
    headlineLarge: _baseHeadingStyle.copyWith(
      fontSize: 32,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnBackground,
      height: 1.25,
    ),
    headlineMedium: _baseHeadingStyle.copyWith(
      fontSize: 28,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnBackground,
      height: 1.29,
    ),
    headlineSmall: _baseHeadingStyle.copyWith(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnBackground,
      height: 1.33,
    ),

    // Title Styles
    titleLarge: _baseTextStyle.copyWith(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnSurface,
      height: 1.27,
    ),
    titleMedium: _baseTextStyle.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnSurface,
      height: 1.50,
      letterSpacing: 0.15,
    ),
    titleSmall: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColors.lightOnSurface,
      height: 1.43,
      letterSpacing: 0.1,
    ),

    // Label Styles
    labelLarge: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColors.lightOnSurface,
      height: 1.43,
      letterSpacing: 0.1,
    ),
    labelMedium: _baseTextStyle.copyWith(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: AppColors.lightOnSurface,
      height: 1.33,
      letterSpacing: 0.5,
    ),
    labelSmall: _baseTextStyle.copyWith(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      color: AppColors.lightOnSurface,
      height: 1.45,
      letterSpacing: 0.5,
    ),

    // Body Styles
    bodyLarge: _baseTextStyle.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppColors.lightOnSurface,
      height: 1.50,
      letterSpacing: 0.15,
    ),
    bodyMedium: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.lightOnSurface,
      height: 1.43,
      letterSpacing: 0.25,
    ),
    bodySmall: _baseTextStyle.copyWith(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: AppColors.lightOnSurfaceVariant,
      height: 1.33,
      letterSpacing: 0.4,
    ),
  );

  // Dark Theme Text Styles
  static final TextTheme darkTextTheme = TextTheme(
    // Display Styles
    displayLarge: _baseHeadingStyle.copyWith(
      fontSize: 57,
      fontWeight: FontWeight.w700,
      color: AppColors.darkOnBackground,
      height: 1.12,
    ),
    displayMedium: _baseHeadingStyle.copyWith(
      fontSize: 45,
      fontWeight: FontWeight.w700,
      color: AppColors.darkOnBackground,
      height: 1.16,
    ),
    displaySmall: _baseHeadingStyle.copyWith(
      fontSize: 36,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnBackground,
      height: 1.22,
    ),

    // Headline Styles
    headlineLarge: _baseHeadingStyle.copyWith(
      fontSize: 32,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnBackground,
      height: 1.25,
    ),
    headlineMedium: _baseHeadingStyle.copyWith(
      fontSize: 28,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnBackground,
      height: 1.29,
    ),
    headlineSmall: _baseHeadingStyle.copyWith(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnBackground,
      height: 1.33,
    ),

    // Title Styles
    titleLarge: _baseTextStyle.copyWith(
      fontSize: 22,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnSurface,
      height: 1.27,
    ),
    titleMedium: _baseTextStyle.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnSurface,
      height: 1.50,
      letterSpacing: 0.15,
    ),
    titleSmall: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColors.darkOnSurface,
      height: 1.43,
      letterSpacing: 0.1,
    ),

    // Label Styles
    labelLarge: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColors.darkOnSurface,
      height: 1.43,
      letterSpacing: 0.1,
    ),
    labelMedium: _baseTextStyle.copyWith(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: AppColors.darkOnSurface,
      height: 1.33,
      letterSpacing: 0.5,
    ),
    labelSmall: _baseTextStyle.copyWith(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      color: AppColors.darkOnSurface,
      height: 1.45,
      letterSpacing: 0.5,
    ),

    // Body Styles
    bodyLarge: _baseTextStyle.copyWith(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppColors.darkOnSurface,
      height: 1.50,
      letterSpacing: 0.15,
    ),
    bodyMedium: _baseTextStyle.copyWith(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.darkOnSurface,
      height: 1.43,
      letterSpacing: 0.25,
    ),
    bodySmall: _baseTextStyle.copyWith(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      color: AppColors.darkOnSurfaceVariant,
      height: 1.33,
      letterSpacing: 0.4,
    ),
  );

  // Custom Text Styles for specific use cases
  static TextStyle get buttonText => _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.1,
  );

  static TextStyle get captionText => _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
  );

  static TextStyle get overlineText => _baseTextStyle.copyWith(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    letterSpacing: 1.5,
  );

  static TextStyle get errorText => _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.lightError,
    letterSpacing: 0.4,
  );

  static TextStyle get successText => _baseTextStyle.copyWith(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.success,
    letterSpacing: 0.4,
  );

  static TextStyle get linkText => _baseTextStyle.copyWith(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.lightPrimary,
    decoration: TextDecoration.underline,
    letterSpacing: 0.25,
  );

  // Gradient Text Style (for special headings)
  static TextStyle get gradientHeading => _baseHeadingStyle.copyWith(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    letterSpacing: -0.5,
  );
}
