import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../controller/generate_controller.dart';

class GeneratePage extends ConsumerWidget {
  const GeneratePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(generateControllerProvider);
    final controller = ref.read(generateControllerProvider.notifier);
    final formKey = GlobalKey<FormBuilderState>();
    return Scaffold(
      appBar: AppBar(title: const Text('Generate Dataset')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FormBuilder(
              key: formKey,
              child: FormBuilderTextField(
                name: 'prompt',
                decoration: const InputDecoration(labelText: 'Prompt'),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: state.isLoading
                  ? null
                  : () {
                      if (formKey.currentState?.saveAndValidate() ?? false) {
                        final prompt = formKey.currentState?.value['prompt'] as String;
                        controller.executeGenerateDataset(prompt);
                      }
                    },
              child: state.isLoading ? const CircularProgressIndicator() : const Text('Generate'),
            ),
            if (state.errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(state.errorMessage, style: const TextStyle(color: Colors.red)),
              ),
            const SizedBox(height: 24),
            if (state.dataset.isNotEmpty)
              Expanded(
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columns: state.dataset.first.keys
                        .map((key) => DataColumn(label: Text(key)))
                        .toList(),
                    rows: state.dataset
                        .map((row) => DataRow(
                              cells: row.values.map((v) => DataCell(Text(v.toString()))).toList(),
                            ))
                        .toList(),
                  ),
                ),
              ),
            if (state.dataset.isNotEmpty)
              Row(
                children: [
                  ElevatedButton(
                    onPressed: () => controller.repository.exportDataset(data: state.dataset, format: 'csv'),
                    child: const Text('Export CSV'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => controller.repository.exportDataset(data: state.dataset, format: 'json'),
                    child: const Text('Export JSON'),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
} 