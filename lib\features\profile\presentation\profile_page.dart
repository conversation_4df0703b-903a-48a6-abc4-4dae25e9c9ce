import 'package:flutter/material.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Text('User profile and API key management coming soon.'),
          Sized<PERSON><PERSON>(height: 16),
          Text('API Key: <not generated>'),
          Si<PERSON><PERSON><PERSON>(height: 8),
          ElevatedButton(
            onPressed: null, // TODO: Implement API key generation
            child: Text('Generate API Key'),
          ),
        ],
      ),
    );
  }
} 