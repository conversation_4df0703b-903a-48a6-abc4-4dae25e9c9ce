// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AppSettings {

 ThemeMode get themeMode; String get language; bool get notificationsEnabled; bool get analyticsEnabled; DataExportFormat get defaultExportFormat; int get maxConcurrentRequests; bool get cacheEnabled; String? get openRouterApiKey;
/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AppSettingsCopyWith<AppSettings> get copyWith => _$AppSettingsCopyWithImpl<AppSettings>(this as AppSettings, _$identity);

  /// Serializes this AppSettings to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AppSettings&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.language, language) || other.language == language)&&(identical(other.notificationsEnabled, notificationsEnabled) || other.notificationsEnabled == notificationsEnabled)&&(identical(other.analyticsEnabled, analyticsEnabled) || other.analyticsEnabled == analyticsEnabled)&&(identical(other.defaultExportFormat, defaultExportFormat) || other.defaultExportFormat == defaultExportFormat)&&(identical(other.maxConcurrentRequests, maxConcurrentRequests) || other.maxConcurrentRequests == maxConcurrentRequests)&&(identical(other.cacheEnabled, cacheEnabled) || other.cacheEnabled == cacheEnabled)&&(identical(other.openRouterApiKey, openRouterApiKey) || other.openRouterApiKey == openRouterApiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,themeMode,language,notificationsEnabled,analyticsEnabled,defaultExportFormat,maxConcurrentRequests,cacheEnabled,openRouterApiKey);

@override
String toString() {
  return 'AppSettings(themeMode: $themeMode, language: $language, notificationsEnabled: $notificationsEnabled, analyticsEnabled: $analyticsEnabled, defaultExportFormat: $defaultExportFormat, maxConcurrentRequests: $maxConcurrentRequests, cacheEnabled: $cacheEnabled, openRouterApiKey: $openRouterApiKey)';
}


}

/// @nodoc
abstract mixin class $AppSettingsCopyWith<$Res>  {
  factory $AppSettingsCopyWith(AppSettings value, $Res Function(AppSettings) _then) = _$AppSettingsCopyWithImpl;
@useResult
$Res call({
 ThemeMode themeMode, String language, bool notificationsEnabled, bool analyticsEnabled, DataExportFormat defaultExportFormat, int maxConcurrentRequests, bool cacheEnabled, String? openRouterApiKey
});




}
/// @nodoc
class _$AppSettingsCopyWithImpl<$Res>
    implements $AppSettingsCopyWith<$Res> {
  _$AppSettingsCopyWithImpl(this._self, this._then);

  final AppSettings _self;
  final $Res Function(AppSettings) _then;

/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? themeMode = null,Object? language = null,Object? notificationsEnabled = null,Object? analyticsEnabled = null,Object? defaultExportFormat = null,Object? maxConcurrentRequests = null,Object? cacheEnabled = null,Object? openRouterApiKey = freezed,}) {
  return _then(_self.copyWith(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,notificationsEnabled: null == notificationsEnabled ? _self.notificationsEnabled : notificationsEnabled // ignore: cast_nullable_to_non_nullable
as bool,analyticsEnabled: null == analyticsEnabled ? _self.analyticsEnabled : analyticsEnabled // ignore: cast_nullable_to_non_nullable
as bool,defaultExportFormat: null == defaultExportFormat ? _self.defaultExportFormat : defaultExportFormat // ignore: cast_nullable_to_non_nullable
as DataExportFormat,maxConcurrentRequests: null == maxConcurrentRequests ? _self.maxConcurrentRequests : maxConcurrentRequests // ignore: cast_nullable_to_non_nullable
as int,cacheEnabled: null == cacheEnabled ? _self.cacheEnabled : cacheEnabled // ignore: cast_nullable_to_non_nullable
as bool,openRouterApiKey: freezed == openRouterApiKey ? _self.openRouterApiKey : openRouterApiKey // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// Adds pattern-matching-related methods to [AppSettings].
extension AppSettingsPatterns on AppSettings {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>(TResult Function( _AppSettings value)?  $default,{required TResult orElse(),}){
final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>(TResult Function( _AppSettings value)  $default,){
final _that = this;
switch (_that) {
case _AppSettings():
return $default(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>(TResult? Function( _AppSettings value)?  $default,){
final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>(TResult Function( ThemeMode themeMode,  String language,  bool notificationsEnabled,  bool analyticsEnabled,  DataExportFormat defaultExportFormat,  int maxConcurrentRequests,  bool cacheEnabled,  String? openRouterApiKey)?  $default,{required TResult orElse(),}) {final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that.themeMode,_that.language,_that.notificationsEnabled,_that.analyticsEnabled,_that.defaultExportFormat,_that.maxConcurrentRequests,_that.cacheEnabled,_that.openRouterApiKey);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>(TResult Function( ThemeMode themeMode,  String language,  bool notificationsEnabled,  bool analyticsEnabled,  DataExportFormat defaultExportFormat,  int maxConcurrentRequests,  bool cacheEnabled,  String? openRouterApiKey)  $default,) {final _that = this;
switch (_that) {
case _AppSettings():
return $default(_that.themeMode,_that.language,_that.notificationsEnabled,_that.analyticsEnabled,_that.defaultExportFormat,_that.maxConcurrentRequests,_that.cacheEnabled,_that.openRouterApiKey);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>(TResult? Function( ThemeMode themeMode,  String language,  bool notificationsEnabled,  bool analyticsEnabled,  DataExportFormat defaultExportFormat,  int maxConcurrentRequests,  bool cacheEnabled,  String? openRouterApiKey)?  $default,) {final _that = this;
switch (_that) {
case _AppSettings() when $default != null:
return $default(_that.themeMode,_that.language,_that.notificationsEnabled,_that.analyticsEnabled,_that.defaultExportFormat,_that.maxConcurrentRequests,_that.cacheEnabled,_that.openRouterApiKey);case _:
  return null;

}
}

}

/// @nodoc
@JsonSerializable()

class _AppSettings implements AppSettings {
  const _AppSettings({this.themeMode = ThemeMode.system, this.language = 'en', this.notificationsEnabled = true, this.analyticsEnabled = true, this.defaultExportFormat = DataExportFormat.csv, this.maxConcurrentRequests = 10, this.cacheEnabled = true, this.openRouterApiKey});
  factory _AppSettings.fromJson(Map<String, dynamic> json) => _$AppSettingsFromJson(json);

@override@JsonKey() final  ThemeMode themeMode;
@override@JsonKey() final  String language;
@override@JsonKey() final  bool notificationsEnabled;
@override@JsonKey() final  bool analyticsEnabled;
@override@JsonKey() final  DataExportFormat defaultExportFormat;
@override@JsonKey() final  int maxConcurrentRequests;
@override@JsonKey() final  bool cacheEnabled;
@override final  String? openRouterApiKey;

/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AppSettingsCopyWith<_AppSettings> get copyWith => __$AppSettingsCopyWithImpl<_AppSettings>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AppSettingsToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AppSettings&&(identical(other.themeMode, themeMode) || other.themeMode == themeMode)&&(identical(other.language, language) || other.language == language)&&(identical(other.notificationsEnabled, notificationsEnabled) || other.notificationsEnabled == notificationsEnabled)&&(identical(other.analyticsEnabled, analyticsEnabled) || other.analyticsEnabled == analyticsEnabled)&&(identical(other.defaultExportFormat, defaultExportFormat) || other.defaultExportFormat == defaultExportFormat)&&(identical(other.maxConcurrentRequests, maxConcurrentRequests) || other.maxConcurrentRequests == maxConcurrentRequests)&&(identical(other.cacheEnabled, cacheEnabled) || other.cacheEnabled == cacheEnabled)&&(identical(other.openRouterApiKey, openRouterApiKey) || other.openRouterApiKey == openRouterApiKey));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,themeMode,language,notificationsEnabled,analyticsEnabled,defaultExportFormat,maxConcurrentRequests,cacheEnabled,openRouterApiKey);

@override
String toString() {
  return 'AppSettings(themeMode: $themeMode, language: $language, notificationsEnabled: $notificationsEnabled, analyticsEnabled: $analyticsEnabled, defaultExportFormat: $defaultExportFormat, maxConcurrentRequests: $maxConcurrentRequests, cacheEnabled: $cacheEnabled, openRouterApiKey: $openRouterApiKey)';
}


}

/// @nodoc
abstract mixin class _$AppSettingsCopyWith<$Res> implements $AppSettingsCopyWith<$Res> {
  factory _$AppSettingsCopyWith(_AppSettings value, $Res Function(_AppSettings) _then) = __$AppSettingsCopyWithImpl;
@override @useResult
$Res call({
 ThemeMode themeMode, String language, bool notificationsEnabled, bool analyticsEnabled, DataExportFormat defaultExportFormat, int maxConcurrentRequests, bool cacheEnabled, String? openRouterApiKey
});




}
/// @nodoc
class __$AppSettingsCopyWithImpl<$Res>
    implements _$AppSettingsCopyWith<$Res> {
  __$AppSettingsCopyWithImpl(this._self, this._then);

  final _AppSettings _self;
  final $Res Function(_AppSettings) _then;

/// Create a copy of AppSettings
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? themeMode = null,Object? language = null,Object? notificationsEnabled = null,Object? analyticsEnabled = null,Object? defaultExportFormat = null,Object? maxConcurrentRequests = null,Object? cacheEnabled = null,Object? openRouterApiKey = freezed,}) {
  return _then(_AppSettings(
themeMode: null == themeMode ? _self.themeMode : themeMode // ignore: cast_nullable_to_non_nullable
as ThemeMode,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,notificationsEnabled: null == notificationsEnabled ? _self.notificationsEnabled : notificationsEnabled // ignore: cast_nullable_to_non_nullable
as bool,analyticsEnabled: null == analyticsEnabled ? _self.analyticsEnabled : analyticsEnabled // ignore: cast_nullable_to_non_nullable
as bool,defaultExportFormat: null == defaultExportFormat ? _self.defaultExportFormat : defaultExportFormat // ignore: cast_nullable_to_non_nullable
as DataExportFormat,maxConcurrentRequests: null == maxConcurrentRequests ? _self.maxConcurrentRequests : maxConcurrentRequests // ignore: cast_nullable_to_non_nullable
as int,cacheEnabled: null == cacheEnabled ? _self.cacheEnabled : cacheEnabled // ignore: cast_nullable_to_non_nullable
as bool,openRouterApiKey: freezed == openRouterApiKey ? _self.openRouterApiKey : openRouterApiKey // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
