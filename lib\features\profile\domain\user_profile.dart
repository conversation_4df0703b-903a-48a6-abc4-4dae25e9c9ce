import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';

@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    required String name,
    required String email,
    String? avatarUrl,
    @Default('') String bio,
    required DateTime createdAt,
    required DateTime lastLoginAt,
    @Default(UserPlan.free) UserPlan plan,
    @Default(UserStats()) UserStats stats,
  }) = _UserProfile;
}

@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    @Default(0) int totalDatasets,
    @Default(0) int totalRows,
    @Default(0) int apiCallsThisMonth,
    @Default(1000) int apiCallsLimit,
  }) = _UserStats;
}

enum UserPlan {
  free,
  pro,
  enterprise,
}
