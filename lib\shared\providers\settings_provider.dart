import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_service.dart';
import '../../features/settings/domain/app_settings.dart';
import 'app_providers.dart';

// Settings Provider
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return SettingsNotifier(storage);
});

class SettingsNotifier extends StateNotifier<AppSettings> {
  final StorageService _storage;
  static const String _settingsKey = 'app_settings';

  SettingsNotifier(this._storage) : super(const AppSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _storage.getObject<AppSettings>(
        _settingsKey,
        (json) => AppSettings.fromJson(json),
      );
      if (settings != null) {
        state = settings;
      }
    } catch (e) {
      // If loading fails, keep default settings
      print('Error loading settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      await _storage.setObject(_settingsKey, state);
    } catch (e) {
      print('Error saving settings: $e');
    }
  }

  Future<void> setThemeMode(ThemeMode themeMode) async {
    state = state.copyWith(themeMode: themeMode);
    await _saveSettings();
  }

  Future<void> setLanguage(String language) async {
    state = state.copyWith(language: language);
    await _saveSettings();
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    state = state.copyWith(notificationsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setAnalyticsEnabled(bool enabled) async {
    state = state.copyWith(analyticsEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setDefaultExportFormat(DataExportFormat format) async {
    state = state.copyWith(defaultExportFormat: format);
    await _saveSettings();
  }

  Future<void> setMaxConcurrentRequests(int max) async {
    state = state.copyWith(maxConcurrentRequests: max);
    await _saveSettings();
  }

  Future<void> setCacheEnabled(bool enabled) async {
    state = state.copyWith(cacheEnabled: enabled);
    await _saveSettings();
  }

  Future<void> setOpenRouterApiKey(String? apiKey) async {
    state = state.copyWith(openRouterApiKey: apiKey);
    await _saveSettings();
  }

  Future<void> resetToDefaults() async {
    state = const AppSettings();
    await _saveSettings();
  }
}
