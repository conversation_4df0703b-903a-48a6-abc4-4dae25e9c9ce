# Synthara: AI-Powered Dataset Generation Platform

## 📌 Project Goal

Synthara is an AI-powered dataset generation platform designed to help users generate high-quality datasets using synthetic data and real-time web data extraction. The core principles are **transparency**, **user experience**, and **reliability**.

---

## 🚀 MVP (Minimum Viable Product)

The MVP version of Synthara should include the following functional components:

### Core Features

* Prompt Input (text-based)
* AI-based query refinement
* Web search integration (Google/Bing/DuckDuckGo)
* Content scraping (Puppeteer)
* Schema detection and dataset generation (CSV/JSON)
* Real-time progress bar / status indicator
* Downloadable data preview
* Error handling and fallback suggestions

### Non-functional MVP Goals

* Responsive and modern UI (light/dark mode)
* User feedback (toast messages, loaders)
* Caching to improve performance

---

## 🎯 Development Roadmap & Cursor Guidelines

### Cursor Development Rules

1. Use atomic commits with meaningful messages.
2. Maintain type safety with strict TypeScript.
3. Modularize logic: separate API calls, scraping logic, and UI.
4. Comment complex logic and scraping flows.
5. Reuse components where possible using shadcn/ui.
6. Keep backend secure: sanitize inputs, validate with Zod.
7. Use ENV for all secrets (e.g., OpenRouter API).

### Development Phases

#### Phase 1: Project Setup (Web)

* Initialize Next.js 14 with TypeScript
* Configure Tailwind CSS, shadcn/ui, and ESLint/Prettier
* Setup environment config with `.env.local`

#### Phase 1: Project Setup (Mobile - Flutter)

* Initialize Flutter project
* Setup folder structure for clean architecture
* Add dependencies: `http`, `provider`, `flutter_hooks`, `flutter_dotenv`
* Configure platform permissions (internet, storage)

#### Phase 2: Core Workflow

* Implement Prompt Form (React Hook Form + Zod / Flutter FormBuilder)
* Connect to OpenRouter API (DeepSeek Chat V3)
* Build web scraping logic with Puppeteer (server-side)
* Implement AI data processing and structuring
* Preview and export dataset (CSV, JSON)

#### Phase 3: UI/UX & Dashboard

* Design modern dashboard with sidebar (Web) / BottomNavBar (Flutter)
* Add pages: Home, Generate, Preview, Profile, Settings
* Build reusable components: StatsCard, Table, Feed, etc.

#### Phase 4: Deployment & Final Touches

* Deploy to Vercel (Web) and Android/iOS stores (Flutter)
* Add analytics (Vercel or Firebase Analytics)
* Enable API key generation for advanced users
* QA testing, bug fixing, and polish

---

## 🛠 Technologies Used

### Frontend (Web)

* **Next.js 14** (App Router, SSR/SSG)
* **Tailwind CSS** + **shadcn/ui**
* **TypeScript**
* **React Hook Form** + **Zod**
* **Lucide React** (icons)

### Mobile (Flutter)

* **Flutter** (cross-platform UI toolkit)
* **Dart** language
* **Provider / Riverpod** (state management)
* **Flutter Hooks** (optional)
* **flutter\_dotenv** (environment variables)
* **CSV / JSON handling packages**

### Backend / AI

* **OpenRouter API** (DeepSeek Chat V3)
* **Node.js** runtime
* **Puppeteer** for scraping
* **Custom Query Optimizer**

### Data Processing

* AI Schema Detection
* Real-time Streaming Updates
* CSV/JSON Generator
* Caching for repeated prompts

### Deployment

* **Vercel** (Edge Functions, analytics, auto deploys)
* **Firebase App Distribution / Play Store / App Store** (Flutter)
* **ENV Vars** for config and API key safety

---

## 🖥️ App Design & Structure

### Pages

* **Homepage**: Clean modern landing page
* **Dashboard**: Sidebar + Topbar layout (Web) / Bottom Navigation (Flutter)
* **Generate Dataset**: Prompt input + real-time status
* **Preview**: Table format data preview with export options
* **Analysis**: Visual/statistical tools (future phase)
* **Profile/Settings**: User profile and API key control
* **Help Center**: FAQs and documentation

### Design System

* **Typography**: 'Space Grotesk' (headers), 'Inter' (body)
* **Color Scheme**: Slate/Dark with bright blue accents
* **Modes**: Light & Dark with glass-morphism UI
* **Components**: Modern cards, enhanced tables, modal dialogs
* **Animations**: Subtle transitions for load/progress

---

## 🧪 Workflow Example

```
Prompt: "Generate a dataset of tech startups in Berlin"
→ AI refines prompt
→ Web sources searched & filtered
→ Puppeteer scrapes structured content
→ AI analyzes & formats dataset
→ Data preview shown
→ User downloads CSV
```

---

## 📁 File References

* `README.md`: Project overview and instructions
* `DESIGN_RESTRUCTURE_SUMMARY.md`: UI/UX standards
* `blueprint.md`: MVP scope, style, features
* `generate-from-web-flow.ts`: Main backend scraping logic

---

## 📫 Need Help?

Reach out via the Help Center or open an issue.

---

> Made with ❤️ using AI, Cursor, Flutter, and Web Magic
