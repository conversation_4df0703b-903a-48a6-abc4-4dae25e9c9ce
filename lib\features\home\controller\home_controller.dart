import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../data/dashboard_repository.dart';
import '../data/dashboard_repository_impl.dart';
import '../domain/dashboard_stats.dart';

part 'home_controller.freezed.dart';

@freezed
class HomeState with _$HomeState {
  const factory HomeState({
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    @Default('') String errorMessage,
    DashboardStats? dashboardStats,
  }) = _HomeState;
}

final homeControllerProvider = StateNotifierProvider<HomeController, HomeState>((ref) {
  return HomeController(DashboardRepositoryImpl());
});

class HomeController extends StateNotifier<HomeState> {
  final DashboardRepository repository;
  
  HomeController(this.repository) : super(const HomeState()) {
    loadDashboardData();
  }

  Future<void> loadDashboardData() async {
    state = state.copyWith(isLoading: true, errorMessage: '');
    try {
      final stats = await repository.getDashboardStats();
      state = state.copyWith(
        dashboardStats: stats,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        errorMessage: e.toString(),
        isLoading: false,
      );
    }
  }

  Future<void> refreshDashboard() async {
    state = state.copyWith(isRefreshing: true, errorMessage: '');
    try {
      await repository.refreshStats();
      final stats = await repository.getDashboardStats();
      state = state.copyWith(
        dashboardStats: stats,
        isRefreshing: false,
      );
    } catch (e) {
      state = state.copyWith(
        errorMessage: e.toString(),
        isRefreshing: false,
      );
    }
  }
}
