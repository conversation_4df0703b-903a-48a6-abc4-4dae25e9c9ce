import 'dart:math';
import 'dashboard_repository.dart';
import '../domain/dashboard_stats.dart';

class DashboardRepositoryImpl implements DashboardRepository {
  @override
  Future<DashboardStats> getDashboardStats() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Mock data for now - replace with actual API calls
    final random = Random();
    return DashboardStats(
      totalDatasets: random.nextInt(50) + 10,
      datasetsThisMonth: random.nextInt(15) + 1,
      totalRows: random.nextInt(10000) + 1000,
      averageAccuracy: 0.85 + (random.nextDouble() * 0.1),
      recentDatasets: await getRecentDatasets(),
    );
  }

  @override
  Future<List<RecentDataset>> getRecentDatasets({int limit = 5}) async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Mock data - replace with actual API calls
    final mockDatasets = [
      RecentDataset(
        id: '1',
        name: 'Tech Startups Berlin',
        prompt: 'Generate a dataset of tech startups in Berlin',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        rowCount: 150,
        status: DatasetStatus.completed,
      ),
      RecentDataset(
        id: '2',
        name: 'E-commerce Products',
        prompt: 'Create dataset of popular e-commerce products',
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        rowCount: 300,
        status: DatasetStatus.completed,
      ),
      RecentDataset(
        id: '3',
        name: 'Restaurant Reviews',
        prompt: 'Generate restaurant reviews dataset',
        createdAt: DateTime.now().subtract(const Duration(hours: 8)),
        rowCount: 75,
        status: DatasetStatus.generating,
      ),
      RecentDataset(
        id: '4',
        name: 'Stock Market Data',
        prompt: 'Financial market data for analysis',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        rowCount: 500,
        status: DatasetStatus.completed,
      ),
      RecentDataset(
        id: '5',
        name: 'Social Media Posts',
        prompt: 'Social media engagement dataset',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        rowCount: 0,
        status: DatasetStatus.failed,
      ),
    ];
    
    return mockDatasets.take(limit).toList();
  }

  @override
  Future<void> refreshStats() async {
    // Simulate refresh operation
    await Future.delayed(const Duration(milliseconds: 800));
  }
}
