import 'package:freezed_annotation/freezed_annotation.dart';

part 'dashboard_stats.freezed.dart';

@freezed
class DashboardStats with _$DashboardStats {
  const factory DashboardStats({
    @Default(0) int totalDatasets,
    @Default(0) int datasetsThisMonth,
    @Default(0) int totalRows,
    @Default(0.0) double averageAccuracy,
    @Default([]) List<RecentDataset> recentDatasets,
  }) = _DashboardStats;
}

@freezed
class RecentDataset with _$RecentDataset {
  const factory RecentDataset({
    required String id,
    required String name,
    required String prompt,
    required DateTime createdAt,
    required int rowCount,
    required DatasetStatus status,
  }) = _RecentDataset;
}

enum DatasetStatus {
  generating,
  completed,
  failed,
  cancelled,
}
