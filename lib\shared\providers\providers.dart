// Main providers export file
// This file exports all providers for easy importing throughout the app

export 'app_providers.dart';
export 'navigation_provider.dart';
export 'settings_provider.dart';

// Feature providers
export '../../features/home/<USER>/home_controller.dart';
export '../../features/generate/controller/generate_controller.dart';

// Additional provider utilities
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Provider observer for debugging
class AppProviderObserver extends ProviderObserver {
  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    print('Provider ${provider.name ?? provider.runtimeType} updated: $newValue');
  }

  @override
  void didAddProvider(
    ProviderBase provider,
    Object? value,
    ProviderContainer container,
  ) {
    print('Provider ${provider.name ?? provider.runtimeType} added: $value');
  }

  @override
  void didDisposeProvider(
    ProviderBase provider,
    ProviderContainer container,
  ) {
    print('Provider ${provider.name ?? provider.runtimeType} disposed');
  }
}
