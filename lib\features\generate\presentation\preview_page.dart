import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../controller/generate_controller.dart';

class PreviewPage extends ConsumerWidget {
  const PreviewPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(generateControllerProvider);
    if (state.dataset.isEmpty) {
      return const Center(child: Text('No data to preview.'));
    }
    return Scaffold(
      appBar: AppBar(title: const Text('Preview Dataset')),
      body: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: state.dataset.first.keys
              .map((key) => DataColumn(label: Text(key)))
              .toList(),
          rows: state.dataset
              .map((row) => DataRow(
                    cells: row.values.map((v) => DataCell(Text(v.toString()))).toList(),
                  ))
              .toList(),
        ),
      ),
    );
  }
} 